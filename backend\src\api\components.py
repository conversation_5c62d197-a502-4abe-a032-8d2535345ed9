"""
Component Discovery API endpoints.

Provides REST API for component search, recommendation, and selection.
Integrates with brainstorming results and updates project state.

TODO: IMPLEMENT - User confirmation and custom component URL support
TODO: ENHANCE - Add caching for component recommendations
TODO: IMPLEMENT - Component comparison and alternative suggestions
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from datetime import datetime

from ..session_manager import get_session_manager, SessionManager
from ..state.project_state import ProjectState, ProjectStatus
from ..modules.component_discovery import create_component_matcher, RecommendedComponent


# Request/Response models
class ComponentSearchRequest(BaseModel):
    """Request model for component search"""
    brainstorming_session_id: str
    max_recommendations: int = 5
    use_real_apis: bool = True


class ComponentSelectionRequest(BaseModel):
    """Request model for component selection"""
    pattern_name: str
    component_full_name: str  # e.g., "awesome-org/fastapi-advanced"
    custom_component_url: Optional[str] = None  # For expert users


class ComponentSearchResponse(BaseModel):
    """Response model for component search results"""
    success: bool
    pattern_count: int
    recommendations: Dict[str, List[Dict[str, Any]]]
    search_metadata: Dict[str, Any]
    error_message: Optional[str] = None


class ComponentSelectionResponse(BaseModel):
    """Response model for component selection"""
    success: bool
    selected_component: Optional[Dict[str, Any]] = None
    project_status: str
    error_message: Optional[str] = None


# Create router
router = APIRouter(prefix="/api/projects/{session_id}/components", tags=["components"])


@router.post("/search", response_model=ComponentSearchResponse)
async def search_components(
    session_id: str,
    request: ComponentSearchRequest,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Search for component recommendations based on brainstorming results.
    
    This endpoint:
    1. Retrieves brainstorming session results
    2. Performs two-stage component discovery
    3. Returns ranked recommendations with health analysis
    """
    try:
        print(f"🔍 Starting component search for session {session_id}")
        
        # Get project state
        project_state = session_manager.get_project(session_id)
        if not project_state:
            raise HTTPException(status_code=404, detail="Project session not found")
        
        # Get brainstorming session data
        brainstorming_session = session_manager.get_brainstorming_session(request.brainstorming_session_id)
        if not brainstorming_session:
            raise HTTPException(status_code=404, detail="Brainstorming session not found")
        
        # Ensure brainstorming is complete
        if not brainstorming_session.conversation_complete:
            raise HTTPException(
                status_code=400, 
                detail="Brainstorming session must be completed before component search"
            )
        
        # Update project status
        project_state.update_status(ProjectStatus.PROCURING, "component_discovery")
        
        # Create component matcher
        component_matcher = create_component_matcher(use_real_apis=request.use_real_apis)
        
        # Perform component discovery
        brainstorming_results = brainstorming_session.to_dict()
        recommendations = await component_matcher.find_best_components(
            brainstorming_results, 
            max_recommendations=request.max_recommendations
        )
        
        # Store candidate components in project state
        candidate_components = {}
        for pattern_name, pattern_recommendations in recommendations.items():
            candidate_components[pattern_name] = [
                rec.to_dict() for rec in pattern_recommendations
            ]
        
        project_state.candidate_components = candidate_components
        
        # Prepare response
        search_metadata = {
            "total_patterns": len(recommendations),
            "total_recommendations": sum(len(recs) for recs in recommendations.values()),
            "brainstorming_session_id": request.brainstorming_session_id,
            "search_timestamp": project_state.last_modified.isoformat()
        }
        
        print(f"✅ Component search complete: {len(recommendations)} patterns, {search_metadata['total_recommendations']} recommendations")
        
        return ComponentSearchResponse(
            success=True,
            pattern_count=len(recommendations),
            recommendations=candidate_components,
            search_metadata=search_metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Component search failed: {e}")
        return ComponentSearchResponse(
            success=False,
            pattern_count=0,
            recommendations={},
            search_metadata={},
            error_message=str(e)
        )


@router.get("/candidates", response_model=Dict[str, Any])
async def get_component_candidates(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Get previously discovered component candidates for a project.
    
    Returns cached component recommendations from the last search.
    """
    try:
        # Get project state
        project_state = session_manager.get_project(session_id)
        if not project_state:
            raise HTTPException(status_code=404, detail="Project session not found")
        
        if not project_state.candidate_components:
            return {
                "success": True,
                "candidates": {},
                "message": "No component candidates found. Run component search first."
            }
        
        return {
            "success": True,
            "candidates": project_state.candidate_components,
            "metadata": {
                "pattern_count": len(project_state.candidate_components),
                "last_search": project_state.last_modified.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Failed to get candidates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/select", response_model=ComponentSelectionResponse)
async def select_component(
    session_id: str,
    request: ComponentSelectionRequest,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Select a component for a specific pattern.
    
    This endpoint:
    1. Validates the component selection
    2. Updates project state with selected component
    3. Prepares for next phase (code generation)
    """
    try:
        print(f"🎯 Selecting component for pattern {request.pattern_name}")
        
        # Get project state
        project_state = session_manager.get_project(session_id)
        if not project_state:
            raise HTTPException(status_code=404, detail="Project session not found")
        
        # Find the selected component in candidates
        selected_component_data = None
        
        if request.custom_component_url:
            # TODO: IMPLEMENT - Handle custom component URLs for expert users
            # For now, return error
            raise HTTPException(
                status_code=501, 
                detail="Custom component URLs not yet implemented"
            )
        else:
            # Find component in existing candidates
            pattern_candidates = project_state.candidate_components.get(request.pattern_name, [])
            
            for candidate in pattern_candidates:
                if candidate["candidate_repo"]["full_name"] == request.component_full_name:
                    selected_component_data = candidate
                    break
            
            if not selected_component_data:
                raise HTTPException(
                    status_code=404,
                    detail=f"Component {request.component_full_name} not found in candidates for pattern {request.pattern_name}"
                )
        
        # Update project state with selection
        project_state.select_component(request.pattern_name, selected_component_data)
        
        # Check if all required patterns have components selected
        required_patterns = list(project_state.candidate_components.keys())
        selected_patterns = list(project_state.selected_components.keys())
        
        if len(selected_patterns) >= len(required_patterns):
            # All components selected, ready for next phase
            project_state.update_status(ProjectStatus.QUILTING, "ready_for_code_generation")
        
        print(f"✅ Component selected: {request.component_full_name} for {request.pattern_name}")
        
        return ComponentSelectionResponse(
            success=True,
            selected_component=selected_component_data,
            project_status=project_state.status.value
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Component selection failed: {e}")
        return ComponentSelectionResponse(
            success=False,
            project_status="error",
            error_message=str(e)
        )


@router.get("/selected", response_model=Dict[str, Any])
async def get_selected_components(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Get currently selected components for the project.
    
    Returns the components that have been chosen for each pattern.
    """
    try:
        # Get project state
        project_state = session_manager.get_project(session_id)
        if not project_state:
            raise HTTPException(status_code=404, detail="Project session not found")
        
        return {
            "success": True,
            "selected_components": project_state.selected_components,
            "metadata": {
                "selection_count": len(project_state.selected_components),
                "project_status": project_state.status.value,
                "last_modified": project_state.last_modified.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Failed to get selected components: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{pattern_name}/selection")
async def deselect_component(
    session_id: str,
    pattern_name: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Remove component selection for a specific pattern.
    
    Allows users to change their mind and select a different component.
    """
    try:
        # Get project state
        project_state = session_manager.get_project(session_id)
        if not project_state:
            raise HTTPException(status_code=404, detail="Project session not found")
        
        # Remove selection if it exists
        if pattern_name in project_state.selected_components:
            del project_state.selected_components[pattern_name]
            project_state.last_modified = datetime.now()
            
            # Update status back to procuring if needed
            if project_state.status == ProjectStatus.QUILTING:
                project_state.update_status(ProjectStatus.PROCURING, "component_reselection")
            
            print(f"✅ Deselected component for pattern {pattern_name}")
            
            return {
                "success": True,
                "message": f"Component selection removed for pattern {pattern_name}",
                "project_status": project_state.status.value
            }
        else:
            return {
                "success": True,
                "message": f"No component was selected for pattern {pattern_name}",
                "project_status": project_state.status.value
            }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Failed to deselect component: {e}")
        raise HTTPException(status_code=500, detail=str(e))
