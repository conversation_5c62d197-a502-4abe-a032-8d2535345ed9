"""
CodeQuilter Backend - Main FastAPI Application

This is the entry point for the CodeQuilter backend server.
Currently implements basic health check and project endpoints.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn

from .api import projects, brainstorming
from .session_manager import session_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle application startup and shutdown events"""
    # Startup
    await session_manager.start()
    yield
    # Shutdown
    await session_manager.stop()

app = FastAPI(
    title="CodeQuilter Engine",
    description="AI-native development environment backend",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan,
)

# Configure CORS for frontend development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(projects.router)
app.include_router(brainstorming.router)


@app.get("/")
async def root():
    """Root endpoint - basic health check"""
    return {
        "message": "CodeQuilter Engine is running",
        "version": "0.1.0",
        "status": "healthy"
    }


@app.get("/api/health")
async def health_check():
    """Health check endpoint for monitoring"""
    return {
        "status": "healthy",
        "service": "codequilter-engine",
        "version": "0.1.0"
    }


# Startup and shutdown events are now handled by the lifespan context manager above


if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
