{"tests/test_main.py::test_mock_projects_endpoint": true, "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_success": true, "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_project_not_found": true, "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_brainstorming_not_found": true, "tests/test_api_components.py::TestComponentSearchAPI::test_search_components_brainstorming_incomplete": true, "tests/test_api_components.py::TestComponentSearchAPI::test_get_component_candidates_success": true, "tests/test_api_components.py::TestComponentSearchAPI::test_get_component_candidates_empty": true, "tests/test_api_components.py::TestComponentSearchAPI::test_select_component_success": true, "tests/test_api_components.py::TestComponentSearchAPI::test_select_component_not_found": true, "tests/test_api_components.py::TestComponentSearchAPI::test_select_component_custom_url_not_implemented": true, "tests/test_api_components.py::TestComponentSearchAPI::test_get_selected_components_success": true, "tests/test_api_components.py::TestComponentSearchAPI::test_get_selected_components_empty": true, "tests/test_api_components.py::TestComponentSearchAPI::test_deselect_component_success": true, "tests/test_api_components.py::TestComponentSearchAPI::test_deselect_component_not_selected": true, "tests/test_api_components.py::TestComponentSearchAPI::test_project_status_transitions": true}